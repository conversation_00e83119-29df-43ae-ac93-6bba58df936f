{"appId": "com.jecstz.talisia-desktop", "productName": "Talisia POS", "files": ["dist-electron/**/*", "dist-react/**/*"], "directories": {"output": "release"}, "win": {"target": ["nsis"], "artifactName": "${productName}-Setup-${version}.${ext}"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "Talisia POS"}, "publish": [{"provider": "github", "owner": "samxtu", "repo": "Talisia-POS"}], "extends": null, "extraMetadata": {"main": "dist-electron/main.js"}, "extraResources": [{"from": "dist-electron", "to": "dist-electron", "filter": ["**/*"]}]}