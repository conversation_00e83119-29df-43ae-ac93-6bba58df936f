{"name": "talisia-desktop", "private": true, "version": "0.0.7", "type": "module", "main": "dist-electron/main.js", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "scripts": {"dev": "npm-run-all --parallel dev:react dev:electron", "dev:react": "vite", "generate": "graphql-codegen --config codegen.yml", "dev:electron": "yarn transpile:electron && cross-env NODE_ENV=development electron .", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "transpile:electron": "tsc --project src/electron/tsconfig.json", "clean": "rimraf dist dist-electron dist-react release", "dist:mac": "yarn clean && yarn transpile:electron && yarn build && electron-builder --mac --arm64", "dist:win": "yarn clean && yarn transpile:electron && yarn build && node build-debug.cjs", "dist:linux": "yarn clean && yarn transpile:electron && yarn build && electron-builder --linux --x64", "publish:w": "yarn clean && yarn transpile:electron && yarn build && electron-builder  --win --x64  -p always", "publish:mac": "yarn clean && yarn transpile:electron && yarn build && electron-builder --mac --arm64 -p always", "publish:win": "yarn clean && yarn transpile:electron && yarn build && node build-debug.cjs --publish && xcopy /E /I /Y api dist\\win-unpacked\\resources\\api", "publish:linux": "yarn clean && yarn transpile:electron && yarn build && electron-builder --linux --x64 -p always"}, "dependencies": {"@ant-design/icons": "^5.2.6", "@apollo/client": "^3.13.1", "@chakra-ui/icons": "^2.0.18", "@chakra-ui/react": "2.5.5", "@chakra-ui/system": "^2.5.5", "@chakra-ui/theme-tools": "^2.0.16", "@emotion/cache": "^11.10.7", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fullcalendar/core": "^6.1.15", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/interaction": "^6.1.15", "@fullcalendar/react": "^6.1.15", "@fullcalendar/resource-timeline": "^6.1.15", "@fullcalendar/timegrid": "^6.1.15", "@mui/icons-material": "^6.4.5", "@mui/material": "^6.4.5", "@supabase/supabase-js": "^2.43.1", "@types/react-helmet": "^6.1.7", "@urql/exchange-graphcache": "^6.0.1", "antd": "^5.10.0", "axios": "^1.7.7", "chakra-react-select": "^5.0.2", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "electron-context-menu": "^4.0.4", "electron-log": "^5.0.1", "electron-updater": "^6.1.7", "file-saver": "^2.0.5", "formik": "^2.2.1", "framer-motion": "^12.4.7", "graphql": "^16.9.0", "https": "^1.0.0", "isomorphic-unfetch": "^3.1.0", "jsbarcode": "^3.11.6", "jspdf": "^2.5.2", "npm-run-all": "^4.1.5", "react": "^18.3.1", "react-confirm-alert": "^3.0.6", "react-custom-scrollbars": "^4.2.1", "react-dom": "^18.3.1", "react-helmet": "^6.1.0", "react-hook-form": "^7.54.2", "react-icons": "^5.3.0", "react-infinite-scroll-component": "^6.1.0", "react-is": "^17.0.1", "react-router": "^5.2.0", "react-router-dom": "^5.2.0", "react-scripts": "4.0.0", "react-select": "^5.7.5", "react-semantic-toasts": "^0.6.6", "react-table": "^7.8.0", "react-to-print": "^3.0.2", "semantic-ui-css": "^2.5.0", "semantic-ui-react": "^2.1.4", "tag": "^0.4.17", "ts-node": "^10.9.1", "urql": "^4.2.1", "web-vitals": "^0.2.4", "workbox-background-sync": "^5.1.3", "workbox-broadcast-update": "^5.1.3", "workbox-cacheable-response": "^5.1.3", "workbox-core": "^5.1.3", "workbox-expiration": "^5.1.3", "workbox-google-analytics": "^5.1.3", "workbox-navigation-preload": "^5.1.3", "workbox-precaching": "^5.1.3", "workbox-range-requests": "^5.1.3", "workbox-routing": "^5.1.3", "workbox-strategies": "^5.1.3", "workbox-streams": "^5.1.3", "xlsx": "^0.18.5", "yup": "^0.29.3"}, "devDependencies": {"@eslint/js": "^9.13.0", "@graphql-codegen/cli": "^3.3.0", "@graphql-codegen/typescript": "^3.0.3", "@graphql-codegen/typescript-operations": "^3.0.3", "@graphql-codegen/typescript-urql": "^3.7.3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^13.0.1", "@types/axios": "^0.14.4", "@types/dotenv": "^8.2.0", "@types/jest": "^29.5.14", "@types/mocha": "^10.0.10", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/react-router": "^5.1.8", "@types/react-router-dom": "^5.1.6", "@types/yup": "^0.29.9", "@vitejs/plugin-react": "^4.3.3", "cross-env": "^7.0.3", "electron": "^33.2.0", "electron-builder": "^25.1.8", "eslint": "^9.13.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.11.0", "rimraf": "^6.0.1", "typescript": "~5.6.2", "typescript-eslint": "^8.11.0", "vite": "^5.4.10"}, "build": {"appId": "com.talisia.desktop", "productName": "<PERSON><PERSON><PERSON> Des<PERSON>op", "files": ["dist-react/**/*", "dist-electron/**/*"], "extraResources": [{"from": "api", "to": "api", "filter": ["**/*"]}], "win": {"target": [{"target": "nsis", "arch": ["x64"]}]}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}}