export const toDate = (dateString: string): string => {
  const date = new Date(dateString);
  const day = String(date.getDate()).padStart(2, "0");
  const month = String(date.getMonth() + 1).padStart(2, "0"); // Months are 0-based
  const year = date.getFullYear();

  return `${day}-${month}-${year}`;
};

export const hasPermission = (
  permissionsArray,
  permissionsToCheck
): boolean => {
  // Check if at least one of the permission names in permissionsToCheck exists in permissionsArray
  return permissionsToCheck.some((permissionName) =>
    permissionsArray.some((p) => p.name === permissionName)
  );
};
