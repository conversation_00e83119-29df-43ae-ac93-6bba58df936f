@echo off
echo Stopping Talisia Desktop API...

:: Find the process and get its PID
for /f "tokens=2" %%a in ('tasklist /fi "imagename eq talisia-api.exe" /nh') do (
    set PID=%%a
)

:: Check if process exists
if defined PID (
    echo Found process with PID: %PID%
    taskkill /F /PID %PID%
    echo Application stopped successfully!
) else (
    echo Application is not running.
)

timeout /t 2 > nul
exit 