import { Link, useLocation } from "react-router-dom";
import {
  Avatar,
  Box,
  Button,
  Flex,
  Grid,
  Icon,
  Switch,
  Text,
  useColorMode,
  useColorModeValue,
  useToast,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  useDisclosure,
  Card,
  CardBody,
  CardHeader,
} from "@chakra-ui/react";

// Icons
import { FaCube } from "react-icons/fa";
import {
  useAddPermissionMutation,
  User,
  useRemovePermissionMutation,
  useGetUserQuery,
  Permission,
  useMeQuery,
} from "../../generated/graphql";
import { useEffect, useState } from "react";
import { hasPermission } from "../../interfaces/Helpers";

interface propState {
  employee: User;
}

const POSProfile: React.FC = () => {
  const state = useLocation().state as propState;
  const userProp = state.employee;

  const [user, setUser] = useState<User | null>(userProp);
  const [{ data, fetching }] = useGetUserQuery({
    variables: { id: user ? user.id : 0 },
    requestPolicy: "network-only",
  });

  const [{ data: meData }] = useMeQuery(); // Fetch current user's permissions
  const me = meData?.me;

  useEffect(() => {
    if (!fetching) setUser(data?.getUser as User);
  }, [fetching, data]);

  const [, addPermission] = useAddPermissionMutation();
  const [, removePermission] = useRemovePermissionMutation();
  const toast = useToast({
    position: "top",
  });

  const { isOpen, onOpen, onClose } = useDisclosure();
  const [selectedPermission, setSelectedPermission] = useState<string | null>(
    null
  );
  const [subPermissionStates, setSubPermissionStates] = useState<{
    [key: string]: boolean;
  }>({});

  const hasPermissionsPermission = me?.permissions?.some(
    (perm) => perm.name === "Permissions" || perm.name === "Administrator"
  );

  const changePermissionStatus = async (permission: string, value: boolean) => {
    if (!user?.id || !hasPermissionsPermission) return;

    try {
      if (value) {
        const response = await addPermission({
          name: permission,
          userId: user.id,
        });
        if (response.data?.addPermission.status) {
          setUser((prev) =>
            prev
              ? {
                  ...prev,
                  permissions: [
                    ...(prev.permissions || []),
                    response.data?.addPermission.permission as Permission,
                  ],
                }
              : null
          );
          toast({
            title: `Permission "${permission}" granted successfully!`,
            variant: "left-accent",
            status: "success",
            isClosable: true,
          });
        } else
          toast({
            title: `Error updating permission "${permission}"`,
            variant: "left-accent",
            status: "error",
            isClosable: true,
          });
      } else {
        const response = await removePermission({
          name: permission,
          userId: user.id,
        });
        if (response.data?.removePermission.status) {
          setUser((prev) =>
            prev
              ? {
                  ...prev,
                  permissions:
                    prev.permissions?.filter(
                      (perm) => perm.name !== permission
                    ) || [],
                }
              : null
          );
          toast({
            title: `Permission "${permission}" revoked successfully!`,
            variant: "left-accent",
            status: "warning",
            isClosable: true,
          });
        } else
          toast({
            title: `Error updating permission "${permission}"`,
            variant: "left-accent",
            status: "error",
            isClosable: true,
          });
      }
    } catch (error) {
      console.error("Error updating permission", error);
      toast({
        title: `Error updating permission "${permission}"`,
        variant: "left-accent",
        status: "error",
        isClosable: true,
      });
    }
  };

  const handleSubPermissionChange = (subPerm: string, value: boolean) => {
    if (!selectedPermission) return;
    const fullPermissionName = `${selectedPermission}>${subPerm}`;
    setSubPermissionStates((prev) => ({
      ...prev,
      [fullPermissionName]: value,
    }));
    changePermissionStatus(fullPermissionName, value);
  };

  const openSubPermissionsModal = (permission: string) => {
    setSelectedPermission(permission);
    const initialStates = (
      subPermissions[permission as keyof typeof subPermissions] || []
    ).reduce(
      (acc, subPerm) => ({
        ...acc,
        [`${permission}>${subPerm}`]:
          user?.permissions?.some(
            (perm) => perm.name === `${permission}>${subPerm}`
          ) || false,
      }),
      {}
    );
    setSubPermissionStates(initialStates);
    onOpen();
  };

  const { colorMode } = useColorMode();
  const textColor = useColorModeValue("gray.700", "white");
  const bgProfile = useColorModeValue("hsla(0,0%,100%,.8)", "navy.800");
  const borderProfileColor = useColorModeValue("white", "transparent");
  const emailColor = useColorModeValue("gray.400", "gray.300");

  const permissionList1 = [
    "POS",
    "Sales",
    "Products",
    "Open Tabs",
    "Counters",
    "Permissions",
    "Pending Orders",
  ];

  const permissionList2 = [
    "Users",
    "Import",
    "Transfer",
    "Categories",
    "Designations",
    "Customer Tags",
    "Expenses",
  ];

  const subPermissions = {
    POS: ["Instant Order", "Hold Order", "Employee Order"],
    Sales: ["All"],
    Products: ["Add", "Edit"],
    "Open Tabs": ["All", "Edit", "Delete"],
    Counters: ["Add", "Edit"],
    Permissions: ["All"],
    "Pending Orders": ["All", "Edit", "No Confirm Dialog", "Delete"],
    Users: ["Add", "Edit"],
    Transfer: ["All"],
    Categories: ["Add", "Edit", "Delete"],
    Designations: ["Add", "Edit", "Delete"],
    "Customer Tags": ["Add", "Edit", "Delete"],
    Expenses: ["All", "Approve"],
  };

  const renderPermissionSwitch = (permission: string) => (
    <Flex align="center" mb="20px" key={permission} justify="space-between">
      <Flex align="center">
        <Switch
          colorScheme="blue"
          me="10px"
          isChecked={user?.permissions?.some(
            (perm) => perm.name === permission
          )}
          onChange={(e) => changePermissionStatus(permission, e.target.checked)}
          isDisabled={!hasPermissionsPermission}
        />
        <Text fontSize="md" color="gray.400" fontWeight="400">
          {permission}
        </Text>
      </Flex>
      {subPermissions[permission as keyof typeof subPermissions]?.length > 0 &&
        user?.permissions?.some((perm) => perm.name === permission) && (
          <Button
            size="sm"
            colorScheme="teal"
            onClick={() => openSubPermissionsModal(permission)}
          >
            Expand
          </Button>
        )}
    </Flex>
  );

  return (
    <Flex
      direction="column"
      pt={{ base: "120px", md: "75px", lg: "100px" }}
      px="24px"
      bg={useColorModeValue("gray.50", "gray.900")}
      flex="1"
      overflowY="auto"
      maxHeight={{ base: "calc(100vh - 0px)", md: "calc(100vh - 0px)" }}
      sx={{
        "&::-webkit-scrollbar": {
          width: "1px",
        },
        "&::-webkit-scrollbar-thumb": {
          background: "gray.300",
          borderRadius: "4px",
        },
        "&::-webkit-scrollbar-thumb:hover": {
          background: "gray.500",
        },
      }}
    >
      {/* Profile Header */}
      <Flex
        direction={{ base: "column", md: "row" }}
        mb="24px"
        maxH="330px"
        justifyContent={{ base: "center", md: "space-between" }}
        align="center"
        backdropFilter="blur(21px)"
        boxShadow="0px 2px 5.5px rgba(0, 0, 0, 0.02)"
        border="1.5px solid"
        borderColor={borderProfileColor}
        bg={bgProfile}
        p="24px"
        borderRadius="20px"
      >
        <Flex
          align="center"
          mb={{ base: "10px", md: "0px" }}
          direction={{ base: "column", md: "row" }}
          w="100%"
          textAlign={{ base: "center", md: "left" }}
          position="relative"
        >
          <Box position="relative">
            <Avatar
              me={{ md: "22px" }}
              src={"https://via.placeholder.com/150?text=No+Image"}
              w="80px"
              h="80px"
              borderRadius="15px"
              boxShadow="md"
            />
          </Box>
          <Flex
            direction="column"
            maxWidth="100%"
            my={{ base: "14px" }}
            ml={{ md: "8px" }}
          >
            <Text
              fontSize={{ base: "lg", lg: "xl" }}
              color={textColor}
              fontWeight="bold"
            >
              {user?.firstname} {user?.lastname}
            </Text>
            <Text
              fontSize={{ base: "sm", md: "md" }}
              color={emailColor}
              fontWeight="semibold"
            >
              {user?.email}
            </Text>
          </Flex>
        </Flex>
        <Flex
          direction={{ base: "column", lg: "row" }}
          w={{ base: "100%", md: "50%", lg: "auto" }}
        >
          <Button
            p="0px"
            bg="transparent"
            variant="no-effects"
            _hover={{ bg: "transparent" }}
            as={Link}
            to={{ pathname: "update-profile", state: { user } }}
          >
            <Flex
              align="center"
              w={{ base: "100%", lg: "135px" }}
              bg={colorMode === "dark" ? "navy.900" : "#fff"}
              borderRadius="8px"
              justifyContent="center"
              py="10px"
              boxShadow="0px 2px 5.5px rgba(0, 0, 0, 0.06)"
              cursor="pointer"
              _hover={{ boxShadow: "md" }}
            >
              <Icon color={textColor} as={FaCube} me="6px" />
              <Text fontSize="xs" color={textColor} fontWeight="bold">
                UPDATE PROFILE
              </Text>
            </Flex>
          </Button>
          <Button
            p="0px"
            bg="transparent"
            variant="no-effects"
            _hover={{ bg: "transparent" }}
            as={Link}
            to="employees"
          >
            <Flex
              align="center"
              w={{ lg: "135px" }}
              borderRadius="15px"
              justifyContent="center"
              py="10px"
              mx={{ lg: "1rem" }}
              cursor="pointer"
              _hover={{ bg: useColorModeValue("gray.100", "gray.700") }}
            >
              <Text fontSize="xs" color={textColor} fontWeight="bold">
                TEAM
              </Text>
            </Flex>
          </Button>
        </Flex>
      </Flex>

      <Grid templateColumns={{ base: "1fr", xl: "repeat(3, 1fr)" }} gap="22px">
        {/* Profile Information Card */}
        <Card p="16px" my={{ base: "24px", xl: "0px" }}>
          <CardHeader p="12px 5px" mb="12px">
            <Text fontSize="lg" color={textColor} fontWeight="bold">
              Profile Information
            </Text>
          </CardHeader>
          <CardBody px="5px">
            <Flex direction="column">
              <Text fontSize="md" color="gray.400" fontWeight="400" mb="30px">
                {user?.firstname} {user?.lastname}. <br /> <br /> Bar maid.
              </Text>
              <Flex align="center" mb="18px">
                <Text
                  fontSize="md"
                  color={textColor}
                  fontWeight="bold"
                  me="10px"
                >
                  Full Name:
                </Text>
                <Text fontSize="md" color="gray.400" fontWeight="400">
                  {user?.firstname} {user?.lastname}
                </Text>
              </Flex>
              <Flex align="center" mb="18px">
                <Text
                  fontSize="md"
                  color={textColor}
                  fontWeight="bold"
                  me="10px"
                >
                  Mobile:
                </Text>
                <Text fontSize="md" color="gray.400" fontWeight="400">
                  {user?.phone || "N/A"}
                </Text>
              </Flex>
              <Flex align="center" mb="18px">
                <Text
                  fontSize="md"
                  color={textColor}
                  fontWeight="bold"
                  me="10px"
                >
                  Email:
                </Text>
                <Text fontSize="md" color="gray.400" fontWeight="400">
                  {user?.email}
                </Text>
              </Flex>
              <Flex align="center" mb="18px">
                <Text
                  fontSize="md"
                  color={textColor}
                  fontWeight="bold"
                  me="10px"
                >
                  Role:
                </Text>
                <Text fontSize="md" color="gray.400" fontWeight="400">
                  {user?.role?.name || "User"}
                </Text>
              </Flex>
            </Flex>
          </CardBody>
        </Card>

        {/* Permissions Card 1 */}
        {(hasPermission(me?.permissions, ["Permissions>All"]) ||
          hasPermission(me?.permissions, ["Permissions"])) &&
          user?.permissions && (
            <Card p="16px">
              <CardHeader p="12px 5px" mb="12px">
                <Text fontSize="lg" color={textColor} fontWeight="bold">
                  Platform Settings
                </Text>
              </CardHeader>
              <CardBody px="5px">
                <Flex direction="column">
                  <Text
                    fontSize="sm"
                    color="gray.400"
                    fontWeight="600"
                    mb="20px"
                  >
                    PERMISSIONS
                  </Text>
                  <Flex direction="column" mb="20px" w="100%">
                    {permissionList1.map((permission) =>
                      renderPermissionSwitch(permission)
                    )}
                  </Flex>
                </Flex>
              </CardBody>
            </Card>
          )}

        {/* Permissions Card 2 */}
        {(hasPermission(me?.permissions, ["Permissions>All"]) ||
          hasPermission(me?.permissions, ["Permissions"])) &&
          user?.permissions && (
            <Card p="16px">
              <CardHeader p="12px 5px" mb="12px">
                <Text fontSize="lg" color={textColor} fontWeight="bold">
                  Platform Settings
                </Text>
              </CardHeader>
              <CardBody px="5px">
                <Flex direction="column">
                  <Text
                    fontSize="sm"
                    color="gray.400"
                    fontWeight="600"
                    mb="20px"
                  >
                    PERMISSIONS
                  </Text>
                  <Flex direction="column" mb="20px" w="100%">
                    {permissionList2.map((permission) =>
                      renderPermissionSwitch(permission)
                    )}
                  </Flex>
                </Flex>
              </CardBody>
            </Card>
          )}
      </Grid>

      {/* Sub-Permissions Modal */}
      <Modal size={"lg"} isOpen={isOpen} onClose={onClose}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>{selectedPermission} Sub-Permissions</ModalHeader>
          <ModalBody>
            <Flex direction="column">
              <i style={{ marginBottom: 15 }}>
                Select one, higher permissions are lower on the list!
              </i>
              {selectedPermission &&
                subPermissions[
                  selectedPermission as keyof typeof subPermissions
                ]?.map((subPerm) => (
                  <Flex align="center" mb="20px" key={subPerm}>
                    <Switch
                      colorScheme="blue"
                      me="10px"
                      isChecked={
                        subPermissionStates[
                          `${selectedPermission}>${subPerm}`
                        ] || false
                      }
                      onChange={(e) =>
                        handleSubPermissionChange(subPerm, e.target.checked)
                      }
                      isDisabled={!hasPermissionsPermission}
                    />
                    <Text fontSize="md" color="gray.400" fontWeight="400">
                      {subPerm}
                    </Text>
                  </Flex>
                ))}
            </Flex>
          </ModalBody>
          <ModalFooter>
            <Button colorScheme="blue" mr={3} onClick={onClose}>
              Close
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Flex>
  );
};

export default POSProfile;
