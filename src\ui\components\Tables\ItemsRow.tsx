import React from "react";
import {
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Box,
  useColorModeValue,
} from "@chakra-ui/react";

interface ItemsRowProps {
  items: Array<{
    id: number;
    name: string;
    unit: string;
    stock: number;
    pieceStock?: number;
    subPieceStock?: number;
    pieceUnit?: string;
    subPieceUnit?: string;
  }>;
}

export const ItemsTableRow: React.FC<ItemsRowProps> = ({ items }) => {
  const borderColor = useColorModeValue("gray.200", "gray.700");

  return (
    <Box overflowX="auto">
      <Table variant="simple">
        <Thead>
          <Tr>
            <Th borderColor={borderColor}>Name</Th>
            <Th borderColor={borderColor}>Unit</Th>
            <Th borderColor={borderColor}>Stock</Th>
            {items.some((item) => item.pieceUnit) && (
              <Th borderColor={borderColor}>Piece Stock</Th>
            )}
            {items.some((item) => item.subPieceUnit) && (
              <Th borderColor={borderColor}>Sub Piece Stock</Th>
            )}
          </Tr>
        </Thead>
        <Tbody>
          {items.map((item) => (
            <Tr key={item.id}>
              <Td borderColor={borderColor}>{item.name}</Td>
              <Td borderColor={borderColor}>{item.unit}</Td>
              <Td borderColor={borderColor}>{item.stock}</Td>
              {items.some((i) => i.pieceUnit) && (
                <Td borderColor={borderColor}>
                  {item.pieceStock} {item.pieceUnit}
                </Td>
              )}
              {items.some((i) => i.subPieceUnit) && (
                <Td borderColor={borderColor}>
                  {item.subPieceStock} {item.subPieceUnit}
                </Td>
              )}
            </Tr>
          ))}
        </Tbody>
      </Table>
    </Box>
  );
};
