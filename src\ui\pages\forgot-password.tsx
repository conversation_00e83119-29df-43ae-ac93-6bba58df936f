/* eslint-disable @typescript-eslint/no-empty-object-type */
import React from "react";
import * as Yup from "yup";
import { Formik } from "formik";
import { Link } from "react-router-dom";
import {
  Button,
  Flex,
  Box,
  Heading,
  Icon,
  Text,
  VStack,
  useColorModeValue,
} from "@chakra-ui/react";
import { FaEnvelope } from "react-icons/fa";
import { InputField } from "../views/POS/components/InputField";

interface IAppProps {}

const ForgotPassword: React.FunctionComponent<IAppProps> = () => {
  const cardBg = useColorModeValue("white", "gray.800");
  const bgGradient = useColorModeValue(
    "linear(to-r, teal.100, blue.200)",
    "linear(to-r, teal.900, blue.800)"
  );
  const formHeadingColor = useColorModeValue("teal.600", "teal.300");

  return (
    <Flex
      align="center"
      justify="center"
      minH="100vh"
      bgGradient={bgGradient}
      p={6}
    >
      <Box
        bg={cardBg}
        boxShadow="2xl"
        borderRadius="xl"
        overflow="hidden"
        maxW={{ base: "90%", md: "50%", lg: "40%" }}
        p={8}
      >
        <Flex justify="center" align="center" mb={6}>
          <Heading
            as="h2"
            size="lg"
            color={formHeadingColor}
            textAlign="center"
            fontWeight="bold"
            letterSpacing="wide"
          >
            <Icon as={FaEnvelope} mr={3} />
            Forgot Password
          </Heading>
        </Flex>
        <Text mb={4} fontSize="md" color="gray.600" textAlign="center">
          Enter the email associated with your account, and we'll send you
          instructions to reset your password.
        </Text>
        <Formik
          initialValues={{ email: "" }}
          onSubmit={async (values) => {
            console.log("values", values);
            // Add logic for password recovery
          }}
          validationSchema={Yup.object({
            email: Yup.string()
              .email("Enter a valid email address!")
              .required("Required"),
          })}
          validateOnChange={false}
        >
          {(props) => (
            <form
              onSubmit={(e) => {
                e.preventDefault();
                props.submitForm();
              }}
            >
              <VStack spacing={4}>
                <InputField
                  name="email"
                  touched={props.touched.email}
                  label="Email"
                  placeholder="Email associated with your account"
                  type="email"
                  error={props.errors.email}
                />
              </VStack>
              <Button
                isLoading={props.isSubmitting}
                colorScheme="teal"
                size="lg"
                type="submit"
                width="full"
                mt={6}
              >
                Recover
              </Button>
            </form>
          )}
        </Formik>
        <Text mt={4} textAlign="center">
          Remember your credentials?{" "}
          <Link to="/login" style={{ color: "#319795", fontWeight: "bold" }}>
            Login
          </Link>
        </Text>
      </Box>
    </Flex>
  );
};

export default ForgotPassword;
