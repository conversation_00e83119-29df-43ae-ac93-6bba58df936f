import { mode, StyleFunctionProps } from "@chakra-ui/theme-tools";
import { ComponentStyleConfig } from "@chakra-ui/react";

export const inputStyles: ComponentStyleConfig = {
  variants: {
    auth: (props: StyleFunctionProps) => ({
      field: {
        bg: mode("white", "navy.700")(props),
        border: "1px solid",
        borderColor: mode("gray.200", "transparent")(props),
        _placeholder: { color: mode("gray.300", "gray.400")(props) },
      },
    }),
    search: (props: StyleFunctionProps) => ({
      field: {
        border: "none",
        py: "11px",
        borderRadius: "inherit",
        _placeholder: { color: mode("gray.300", "gray.400")(props) },
      },
    }),
  },
  baseStyle: {
    field: {
      fontWeight: 400,
    },
  },
};
