import { Dispatch } from "react";
import { ActionType, IAction, IUploadingUrl } from "./Types";

const clearUploadingFileUrl = (dispatch: Dispatch<IAction>) =>
  dispatch({ type: ActionType.CLEAR_UPLOADING_FILE_URL });

const setUploadingFileUrl = (
  dispatch: Dispatch<IAction>,
  pendingFile: IUploadingUrl
) =>
  dispatch({
    type: ActionType.SET_UPLOADING_FILE_URL,
    pendingFile: pendingFile,
  });

export { clearUploadingFileUrl, setUploadingFileUrl };
