import React from "react";
import {
  Input,
  InputGroup,
  InputLeftElement,
  useColorModeValue,
  InputGroupProps,
} from "@chakra-ui/react";
import { SearchIcon } from "@chakra-ui/icons";

interface SearchBarProps extends Omit<InputGroupProps, "children"> {
  placeholder?: string;
  onChange?: (value: React.ChangeEvent<HTMLInputElement>) => void;
  value?: string;
}

export function SearchBar({
  placeholder = "Type here...",
  onChange,
  value,
  ...rest
}: SearchBarProps) {
  // Chakra Color Mode
  const searchIconColor = useColorModeValue("gray.700", "gray.200");
  const inputBg = useColorModeValue("white", "navy.800");

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    onChange?.(event);
  };

  return (
    <InputGroup borderRadius="8px" w="200px" {...rest}>
      <InputLeftElement pointerEvents="none">
        <SearchIcon color={searchIconColor} w="15px" h="15px" />
      </InputLeftElement>
      <Input
        value={value}
        onChange={handleChange}
        variant="search"
        fontSize="xs"
        bg={inputBg}
        placeholder={placeholder}
      />
    </InputGroup>
  );
}
