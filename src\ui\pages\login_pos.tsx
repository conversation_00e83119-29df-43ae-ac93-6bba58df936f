/* eslint-disable prefer-const */
/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useEffect, useRef, useState } from "react";
import {
  Box,
  Heading,
  Text,
  VStack,
  Input,
  Button,
  Flex,
  Grid,
  useColorModeValue,
  useBreakpointValue,
  keyframes,
  InputGroup,
  InputLeftElement,
  Spinner,
  Avatar,
  Divider,
  Icon,
  ScaleFade,
  SimpleGrid,
  Badge,
  HStack,
  useToast,
  Tooltip,
} from "@chakra-ui/react";
import { Link, useHistory } from "react-router-dom";
import {
  useLoginMutation,
  useGetUndetailedEmployeesQuery,
  useGetActivePaymentQuery,
} from "../generated/graphql";
import {
  FaArrowLeft,
  FaLock,
  FaUser,
  FaWifi,
  FaServer,
  FaExclamationTriangle,
} from "react-icons/fa";
import { PiWifiSlash } from "react-icons/pi";
import loginImage from "../assets/img/login.jpg";
import { decrypt, encrypt } from "../utils/Helpers";
import { getApiUrl } from "../utils/CreateUrqlClient";

// Animation for background gradient
const gradientShift = keyframes`
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
`;

const SignInPage: React.FC = () => {
  const [password, setPassword] = useState("");
  const [email, setEmail] = useState("");
  const [error, setError] = useState("");
  const [cachedCompanyId, setCachedCompanyId] = useState<number>(0);
  const [cachedCompanySub, setCachedCompanySub] = useState("");
  const [selectedUser, setSelectedUser] = useState<{
    firstname: string;
    lastname: string;
    email: string;
    image?: string | null;
  } | null>(null);
  const toast = useToast({ position: "top", duration: 3000 });
  const [{ fetching }, signIn] = useLoginMutation();
  const [{ data, fetching: fetchingEmployees }, reFetchEmployees] =
    useGetUndetailedEmployeesQuery({
      variables: { companyId: cachedCompanyId },
      requestPolicy: "network-only",
    });
  const [{ data: subscriptionData }, refetchSubscription] =
    useGetActivePaymentQuery({
      requestPolicy: "network-only",
      variables: { companyId: cachedCompanyId },
    });
  const history = useHistory();
  const passwordInputRef = useRef<HTMLInputElement>(null);

  // Add network status state to the main component
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [apiStatus, setApiStatus] = useState<
    "connected" | "disconnected" | "checking"
  >("checking");
  const [electronNetworkStatus, setElectronNetworkStatus] = useState<{
    online: boolean;
    details: { dns: boolean; internet: boolean };
  } | null>(null);

  // Add network status listener
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener("online", handleOnline);
    window.addEventListener("offline", handleOffline);

    return () => {
      window.removeEventListener("online", handleOnline);
      window.removeEventListener("offline", handleOffline);
    };
  }, []);

  // Add API health check with periodic rechecking
  useEffect(() => {
    let isMounted = true;
    let intervalId: NodeJS.Timeout;

    const checkApiConnection = async () => {
      // Use Electron's network status if available, otherwise use browser's
      const networkOnline = electronNetworkStatus
        ? electronNetworkStatus.online
        : isOnline;

      if (!networkOnline) {
        setApiStatus("disconnected");
        return;
      }

      setApiStatus("checking");

      try {
        // Get the API URL based on deployment type
        const apiUrl = await getApiUrl();

        const response = await fetch(apiUrl, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            query: "{ __typename }",
          }),
          signal: AbortSignal.timeout(5000),
        });

        if (isMounted) {
          setApiStatus(response.ok ? "connected" : "disconnected");
        }
      } catch (error) {
        if (isMounted) {
          setApiStatus("disconnected");
        }
      }
    };

    // Initial check
    checkApiConnection();

    // Set up periodic checking every 10 seconds
    intervalId = setInterval(checkApiConnection, 10000);

    return () => {
      isMounted = false;
      clearInterval(intervalId);
    };
  }, [isOnline, electronNetworkStatus]);

  // Add Electron network status check with periodic rechecking
  useEffect(() => {
    let intervalId: NodeJS.Timeout;

    const checkElectronNetworkStatus = async () => {
      if (window.electron) {
        try {
          const status = await window.electron.invoke(
            "check-network-status",
            {}
          );
          setElectronNetworkStatus(status);
          setIsOnline(status.online); // Override browser's status with Electron's
        } catch (err) {
          console.error("Failed to check network status:", err);
        }
      }
    };

    // Initial check
    checkElectronNetworkStatus();

    // Set up periodic checking every 10 seconds
    intervalId = setInterval(checkElectronNetworkStatus, 60000);

    // Also listen for status changes from Electron
    if (window.electron) {
      window.electron.on("network-status-change", (status) => {
        setElectronNetworkStatus(status);
        setIsOnline(status.online);
      });
    }

    return () => {
      clearInterval(intervalId);
    };
  }, []);

  // Network status indicators component that uses the parent component's state
  const NetworkStatusIndicators = () => {
    // Internet status indicator
    const InternetStatusIndicator = () => {
      // Prioritize Electron's network status
      const networkOnline = electronNetworkStatus
        ? electronNetworkStatus.online
        : isOnline;

      return (
        <Tooltip
          label={
            <Box>
              <Text>
                {networkOnline
                  ? "Internet connected"
                  : "No internet connection"}
              </Text>
            </Box>
          }
          placement="top"
          hasArrow
        >
          <Button
            variant="ghost"
            leftIcon={networkOnline ? <FaWifi /> : <PiWifiSlash />}
            size="sm"
            color={networkOnline ? "green.500" : "red.500"}
          >
            {networkOnline ? "Internet" : "No Internet"}
          </Button>
        </Tooltip>
      );
    };

    // API status indicator
    const ApiStatusIndicator = () => {
      let icon, label, color;

      if (apiStatus === "checking") {
        icon = <Spinner size="xs" />;
        label = "Checking server connection...";
        color = "yellow.500";
      } else if (apiStatus === "connected") {
        icon = <FaServer />;
        label = "Connected to server";
        color = "green.500";
      } else {
        icon = <FaExclamationTriangle />;
        label = "Server unreachable";
        color = "orange.500";
      }

      return (
        <Tooltip label={label} placement="top" hasArrow>
          <Button variant="ghost" leftIcon={icon} size="sm" color={color}>
            {apiStatus === "connected"
              ? "API Online"
              : apiStatus === "checking"
              ? "API Checking..."
              : "API Offline"}
          </Button>
        </Tooltip>
      );
    };

    return (
      <HStack spacing={2}>
        <InternetStatusIndicator />
        <ApiStatusIndicator />
      </HStack>
    );
  };

  const fetchCachedCompanyId = async () => {
    try {
      const result = await window.electron.invoke("get-company-id", {});
      const subResult = await window.electron.invoke("get-company-sub", {});
      if (subResult.company_sub) {
        const sub = decrypt(subResult.company_sub);
        setCachedCompanySub(sub);
      }
      if (result.companyId) {
        setCachedCompanyId(Number(decrypt(result.companyId)));
        reFetchEmployees({ companyId: decrypt(result.companyId) });
      } else history.push("login");
    } catch (error) {
      console.error("IPC error loading company ID:", error);
    }
  };

  useEffect(() => {
    fetchCachedCompanyId();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Theme values
  const bgGradient = useColorModeValue(
    "linear(to-r, teal.400, purple.500)",
    "linear(to-r, teal.800, purple.900)"
  );
  const cardBg = useColorModeValue("whiteAlpha.900", "gray.800");
  const headingColor = useColorModeValue("teal.600", "teal.300");
  const buttonSize = useBreakpointValue({ base: "md", md: "lg" });

  // Handle user selection
  const handleUserSelect = (employee: {
    firstname: string;
    lastname: string;
    email: string;
    image?: string | null;
  }) => {
    setSelectedUser(employee);
    setEmail(employee.email);
    // Focus on password input after selecting user
    setTimeout(() => {
      if (passwordInputRef.current) {
        passwordInputRef.current.focus();
      }
    }, 300);
  };

  // Handle back to user selection
  const handleBackToUsers = () => {
    setSelectedUser(null);
    setEmail("");
    setPassword("");
    setError("");
  };
  // Handle sign-in submission
  const handleSignIn = async () => {
    console.log("this is loading the submit process", cachedCompanySub);
    if (cachedCompanySub === "expired") {
      if (subscriptionData?.getActivePayment?.id) {
        const encryptedData = encrypt("active");
        await window.electron.invoke("save-company-sub", encryptedData);
      }
      await refetchSubscription();
      if (subscriptionData?.getActivePayment?.id) {
        const encryptedData = encrypt("active");
        console.log(
          "this is loading the active subscription",
          subscriptionData.getActivePayment
        );
        await window.electron.invoke("save-company-sub", encryptedData);
      } else return history.push("/subscription-expired");
    } else if (cachedCompanySub === "") {
      if (subscriptionData?.getActivePayment?.id) {
        const encryptedData = encrypt("active");
        await window.electron.invoke("save-company-sub", encryptedData);
      } else
        return toast({
          title:
            "No active subscription found for your company, contact support!",
          status: "error",
          isClosable: true,
        });
    }
    const response = await signIn({
      params: {
        email,
        password,
      },
    });
    console.log("this is loading the response", response);
    if (response.error) {
      console.log("this is loading the error", response.error);
    } else if (response.data?.login.error) {
      setError(response.data.login.error.message);
      console.log(
        "this is loading the error",
        response.data.login.error.message
      );
    } else if (response.data?.login.user) {
      console.log("this is loading the POS page", response.data.login.user);
      history.replace("/desktop/POS");
    }
  };

  // Keypad button handler
  const handleKeypadPress = (value: string) => {
    if (value === "clear") {
      setPassword("");
    } else if (value === "enter") {
      handleSignIn();
    } else {
      setPassword((prev) => prev + value);
    }
  };

  return (
    <Flex
      minH="100vh"
      align="center"
      justify="center"
      bgGradient={bgGradient}
      animation={`${gradientShift} 15s ease infinite`}
      backgroundSize="200% 200%"
      p={4}
    >
      <Box
        bg={cardBg}
        boxShadow="2xl"
        borderRadius="2xl"
        overflow="hidden"
        maxW={{ base: "95%", sm: "85%", md: "75%", lg: "70%" }} // Adjusted for wider card
        w="100%" // Ensures it takes full available width up to maxW
        display="flex"
        flexDir={{ base: "column", md: "row" }}
      >
        {/* Left Image Section */}
        <Box
          w={{ base: "100%", md: "40%" }}
          h={{ base: "200px", md: "auto" }}
          bgImage={`url(${loginImage})`}
          bgSize="cover"
          bgPosition="center"
          borderRadius={{ base: "2xl 2xl 0 0", md: "2xl 0 0 2xl" }}
        />

        {/* Right Sign-In Section */}
        <Box
          w={{ base: "100%", md: "60%" }}
          p={6}
          display="flex"
          flexDir="column"
          justifyContent="center"
          bgGradient="linear(to-b, white, gray.50)"
          borderRadius={{ base: "0 0 2xl 2xl", md: "0 2xl 2xl 0" }}
        >
          <VStack spacing={6} align="stretch" w="full">
            <Flex justify="space-between" align="center">
              <Heading as="h2" size="lg" color="teal.600">
                {email ? "Enter Password" : "Select Your Profile"}
              </Heading>
              <NetworkStatusIndicators />
            </Flex>

            <Divider />

            {fetchingEmployees ? (
              <Flex justify="center" align="center" minH="300px">
                <VStack spacing={4}>
                  <Spinner size="xl" color="teal.500" thickness="4px" />
                  <Text color="gray.600">Loading users...</Text>
                </VStack>
              </Flex>
            ) : email ? (
              <VStack spacing={5} align="center">
                <Flex w="full" justify="space-between" align="center">
                  <HStack>
                    <Button
                      leftIcon={<FaArrowLeft />}
                      variant="ghost"
                      colorScheme="teal"
                      size="lg"
                      onClick={handleBackToUsers}
                      borderRadius="lg"
                    >
                      Back to
                    </Button>

                    <Badge
                      onClick={handleBackToUsers}
                      as={Button}
                      colorScheme="teal"
                      fontSize="lg"
                      p={2}
                      borderRadius="md"
                    >
                      Users
                    </Badge>
                  </HStack>
                </Flex>

                <Heading
                  as="h2"
                  size={"lg"}
                  color={headingColor}
                  fontWeight="extrabold"
                  textAlign="center"
                  letterSpacing="wider"
                >
                  Welcome{" "}
                  {selectedUser?.firstname + " " + selectedUser?.lastname}
                </Heading>

                {/* Rest of the password entry UI */}
                <Text fontSize="md" color={"gray.600"} textAlign="center">
                  Enter your password to sign in
                </Text>

                {/* Password Input with Icon */}
                <InputGroup maxW="200px">
                  <InputLeftElement pointerEvents="none">
                    <FaLock color={"teal.500"} />
                  </InputLeftElement>
                  <Input
                    ref={passwordInputRef}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="••••"
                    type="password"
                    size="lg"
                    fontSize="2xl"
                    textAlign="center"
                    letterSpacing="0.2em"
                    fontFamily="monospace"
                    bg={"gray.100"}
                    border="2px solid"
                    borderColor={"teal.300"}
                    focusBorderColor={"teal.500"}
                    isReadOnly // Users can only use keypad
                  />
                </InputGroup>

                {/* Error Message */}
                {error && (
                  <Text color="red.500" fontWeight="bold" textAlign="center">
                    {error}
                  </Text>
                )}

                {/* Keypad */}
                <Grid
                  templateColumns="repeat(3, 1fr)"
                  gap={4}
                  maxW="250px"
                  mx="auto"
                >
                  {[
                    "1",
                    "2",
                    "3",
                    "4",
                    "5",
                    "6",
                    "7",
                    "8",
                    "9",
                    "0",
                    "clear",
                    "enter",
                  ].map((key) => (
                    <Button
                      key={key}
                      onClick={() => handleKeypadPress(key)}
                      colorScheme={
                        key === "enter"
                          ? "teal"
                          : key === "clear"
                          ? "red"
                          : "gray"
                      }
                      size={buttonSize}
                      fontSize="xl"
                      fontWeight="bold"
                      borderRadius="full"
                      boxShadow="md"
                      _hover={{ transform: "scale(1.05)", boxShadow: "lg" }}
                      transition="all 0.2s"
                    >
                      {key === "clear" ? "C" : key === "enter" ? "✓" : key}
                    </Button>
                  ))}
                </Grid>

                {/* Quick Sign-In Button */}
                <Button
                  colorScheme="teal"
                  size="lg"
                  width="full"
                  mt={4}
                  leftIcon={<FaLock />}
                  onClick={handleSignIn}
                  isLoading={fetching}
                  borderRadius="full"
                  boxShadow="lg"
                  _hover={{ bg: "teal.600", transform: "translateY(-2px)" }}
                  transition="all 0.3s"
                >
                  Sign In
                </Button>
                <Button
                  as={Link}
                  to="login"
                  variant={"ghost"}
                  width={"full"}
                  color={"blue.400"}
                >
                  Login
                </Button>
              </VStack>
            ) : data && data.getUndetailedEmployees.length > 0 ? (
              <Box
                maxH="350px"
                overflowY="auto"
                px={2}
                py={4}
                sx={{
                  "&::-webkit-scrollbar": {
                    width: "8px",
                    borderRadius: "8px",
                  },
                  "&::-webkit-scrollbar-thumb": {
                    backgroundColor: "rgba(0,0,0,0.1)",
                    borderRadius: "8px",
                  },
                  "&::-webkit-scrollbar-track": {
                    backgroundColor: "rgba(0,0,0,0.05)",
                    borderRadius: "8px",
                  },
                }}
              >
                <SimpleGrid
                  columns={{ base: 1, sm: 2, md: 2, lg: 3 }}
                  spacing={4}
                >
                  {data.getUndetailedEmployees.map((employee, index) => (
                    <ScaleFade in={true} initialScale={0.9} key={index}>
                      <Box
                        bg="white"
                        borderRadius="xl"
                        boxShadow="md"
                        p={4}
                        cursor="pointer"
                        transition="all 0.3s"
                        _hover={{
                          transform: "translateY(-5px)",
                          boxShadow: "lg",
                          borderColor: "teal.400",
                          borderWidth: "2px",
                        }}
                        onClick={() => handleUserSelect(employee)}
                        borderWidth="1px"
                        borderColor="gray.200"
                        display="flex"
                        flexDirection="column"
                        alignItems="center"
                        justifyContent="center"
                      >
                        <Avatar
                          size="xl"
                          name={`${employee.firstname} ${employee.lastname}`}
                          src={employee.image || undefined}
                          mb={3}
                          bg="teal.100"
                          icon={<Icon as={FaUser} color="teal.500" />}
                          borderWidth={2}
                          borderColor="teal.100"
                        />
                        <VStack spacing={1}>
                          <Text
                            fontWeight="bold"
                            fontSize="md"
                            color="gray.700"
                            textAlign="center"
                          >
                            {employee.firstname} {employee.lastname}
                          </Text>
                        </VStack>
                      </Box>
                    </ScaleFade>
                  ))}
                </SimpleGrid>
              </Box>
            ) : (
              <Flex
                direction="column"
                align="center"
                justify="center"
                minH="300px"
              >
                <Icon
                  as={FaExclamationTriangle}
                  color="orange.500"
                  boxSize={12}
                  mb={4}
                />
                <Heading size="md" textAlign="center" mb={2} color="gray.700">
                  {apiStatus === "disconnected"
                    ? "Cannot Connect to Server"
                    : "No Employees Found"}
                </Heading>
                <Text textAlign="center" color="gray.600" mb={6}>
                  {apiStatus === "disconnected"
                    ? "Please check if your API is running and try again."
                    : "No employees are registered for this company."}
                </Text>
                <Button
                  as={Link}
                  to="/login"
                  variant="outline"
                  colorScheme="blue"
                  width="full"
                  mt={4}
                  borderRadius="lg"
                >
                  Switch to Email Login
                </Button>
                {apiStatus === "disconnected" && (
                  <Button
                    onClick={() => window.location.reload()}
                    colorScheme="teal"
                    width="full"
                    mt={4}
                    borderRadius="lg"
                  >
                    Retry Connection
                  </Button>
                )}
              </Flex>
            )}

            <Button
              as={Link}
              to="/login"
              variant="outline"
              colorScheme="blue"
              width="full"
              mt={4}
              borderRadius="lg"
              display={
                email || !data || data.getUndetailedEmployees.length === 0
                  ? "none"
                  : "block"
              }
            >
              Switch to Email Login
            </Button>
          </VStack>
        </Box>
      </Box>
    </Flex>
  );
};

export default SignInPage;
