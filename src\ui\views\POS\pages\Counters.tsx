import {
  <PERSON>ton,
  Card,
  CardBody,
  CardHeader,
  Flex,
  Grid,
  GridItem,
  Icon,
  Text,
  useColorModeValue,
} from "@chakra-ui/react";
import { FaPlus } from "react-icons/fa";
import { Link } from "react-router-dom";
import { useContext, useEffect, useState } from "react";
import { Store, useGetStoresQuery } from "../../../generated/graphql";
import { CounterListCard } from "../components/CounterCard";
import { hasPermission } from "../../../interfaces/Helpers";
import { MeContext } from "../../../components/Wrapper";
import { SearchBar } from "../components/SearchBar";

const CountersPage = () => {
  const [{ data, fetching, error }] = useGetStoresQuery({
    requestPolicy: "network-only",
  });
  const [counters, setCounters] = useState<Store[]>([]);
  const [searchQuery, setSearchQuery] = useState(""); // Added searchQuery state
  const textColor = useColorModeValue("gray.700", "white");
  const bgProfile = useColorModeValue("hsla(0,0%,100%,.8)", "navy.800");
  const borderProfileColor = useColorModeValue("white", "transparent");
  const me = useContext(MeContext);

  useEffect(() => {
    if (data) {
      setCounters(data.getStores as Store[]);
    }
  }, [data]);

  // Filter counters based on search query
  const filteredCounters = counters.filter((counter) =>
    counter.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value); // Update the search query state
  };

  if (fetching) return <p>Loading...</p>;
  if (error) return <p>Error: {error.message}</p>;

  return (
    <Flex
      direction="column"
      pt={{ base: "20px", md: "25px" }}
      pb="0px"
      flex="1" // Take remaining vertical space
      overflowY="auto"
      maxHeight={{ base: "calc(100vh - 0px)", md: "calc(100vh - 0px)" }}
      sx={{
        "&::-webkit-scrollbar": {
          width: "1px",
        },
        "&::-webkit-scrollbar-thumb": {
          background: "gray.300",
          borderRadius: "4px",
        },
        "&::-webkit-scrollbar-thumb:hover": {
          background: "gray.500",
        },
      }}
    >
      <Card
        overflowX={{ sm: "scroll", xl: "hidden" }}
        pb="0px"
        px={{ base: "20px", md: "40px" }}
      >
        <CardHeader p="0px 0px 8px 0px">
          <Flex
            direction={{ sm: "column", md: "row" }}
            mb="12px"
            maxH="330px"
            justifyContent={{ sm: "center", md: "space-between" }}
            align="center"
            backdropFilter="blur(21px)"
            boxShadow="0px 2px 5.5px rgba(0, 0, 0, 0.02)"
            border="1.5px solid"
            borderColor={borderProfileColor}
            bg={bgProfile}
            p="24px"
            borderRadius="20px"
          >
            <Flex
              align="center"
              mb={{ sm: "10px", md: "0px" }}
              direction={{ sm: "column", md: "row" }}
              w={{ sm: "100%" }}
              textAlign={{ sm: "center", md: "start" }}
            >
              <Text fontSize="xl" fontWeight="bold">
                Our Counters
              </Text>
            </Flex>
            <Flex
              direction={{ sm: "column", lg: "row" }}
              w={{ sm: "100%", md: "50%", lg: "auto" }}
            >
              <SearchBar
                placeholder="Search counters ..."
                value={searchQuery}
                onChange={handleSearchChange} // Set up search input handler
              />

              <Link to={`add-counter`}>
                {hasPermission(me?.permissions, [
                  "Counters>Add",
                  "Counters>Edit",
                ]) && (
                  <Button p="2px" bg="transparent" variant="no-effects">
                    <Flex
                      align="center"
                      w={{ lg: "135px" }}
                      borderRadius="15px"
                      justifyContent="center"
                      py="10px"
                      cursor="pointer"
                    >
                      <Icon
                        as={FaPlus}
                        color={textColor}
                        fontSize="xs"
                        mr="4px"
                      />
                      <Text fontSize="xs" color={textColor} fontWeight="bold">
                        ADD NEW COUNTER
                      </Text>
                    </Flex>
                  </Button>
                )}
              </Link>
            </Flex>
          </Flex>
        </CardHeader>
        <CardBody px="5px">
          <Grid
            templateColumns={{ sm: "1fr", md: "1fr 1fr", xl: "repeat(4, 1fr)" }}
            templateRows={{ sm: "1fr 1fr 1fr auto", md: "1fr 1fr", xl: "1fr" }}
            gap="24px"
          >
            {filteredCounters.length > 0 ? (
              filteredCounters.map((counter) => (
                <CounterListCard key={counter.id} data={counter} />
              ))
            ) : (
              <GridItem colSpan={1} m={0}>
                <Link to={`add-counter`}>
                  <Button
                    p="40px"
                    py={40}
                    bg="transparent"
                    border="1px solid lightgray"
                    borderRadius="15px"
                    minHeight={{ sm: "200px", md: "100%" }}
                  >
                    <Flex
                      direction="column"
                      justifyContent="center"
                      align="center"
                    >
                      <Icon
                        as={FaPlus}
                        mb={10}
                        color={textColor}
                        fontSize="lg"
                        mr="12px"
                      />
                      <Text fontSize="lg" color={textColor} fontWeight="bold">
                        No Counters Found, Add One Now
                      </Text>
                    </Flex>
                  </Button>
                </Link>
              </GridItem>
            )}

            {filteredCounters.length === 0 && counters.length > 0 && (
              <GridItem colSpan={1} m={0}>
                <Link to={`add-counter`}>
                  <Button
                    p="40px"
                    py={40}
                    bg="transparent"
                    border="1px solid lightgray"
                    borderRadius="15px"
                    minHeight={{ sm: "200px", md: "100%" }}
                  >
                    <Flex
                      direction="column"
                      justifyContent="center"
                      align="center"
                    >
                      <Icon
                        as={FaPlus}
                        mb={10}
                        color={textColor}
                        fontSize="lg"
                        mr="12px"
                      />
                      <Text fontSize="lg" color={textColor} fontWeight="bold">
                        No Counters Found Matching "{searchQuery}"
                      </Text>
                    </Flex>
                  </Button>
                </Link>
              </GridItem>
            )}
          </Grid>
        </CardBody>
      </Card>
    </Flex>
  );
};

export default CountersPage;
