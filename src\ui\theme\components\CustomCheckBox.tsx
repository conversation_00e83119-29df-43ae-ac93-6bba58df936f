import React from "react";
import { Checkbox, Icon, CheckboxProps } from "@chakra-ui/react";

interface CustomIconProps extends CheckboxProps {
  isIndeterminate?: boolean;
  isChecked?: boolean;
}

const CustomIcon: React.FC<CustomIconProps> = (props) => {
  const { isIndeterminate, isChecked, ...rest } = props;

  const d = isIndeterminate
    ? "M12,0A12,12,0,1,0,24,12,12.013,12.013,0,0,0,12,0Zm0,19a1.5,1.5,0,1,1,1.5-1.5A1.5,1.5,0,0,1,12,19Zm1.6-6.08a1,1,0,0,0-.6.917,1,1,0,1,1-2,0,3,3,0,0,1,1.8-2.75A2,2,0,1,0,10,9.255a1,1,0,1,1-2,0,4,4,0,1,1,5.6,3.666Z"
    : "M0,12a1.5,1.5,0,0,0,1.5,1.5h8.75a.25.25,0,0,1,.25.25V22.5a1.5,1.5,0,0,0,3,0V13.75a.25.25,0,0,1,.25-.25H22.5a1.5,1.5,0,0,0,0-3H13.75a.25.25,0,0,1-.25-.25V1.5a1.5,1.5,0,0,0-3,0v8.75a.25.25,0,0,1-.25.25H1.5A1.5,1.5,0,0,0,0,12Z";

  return (
    <Icon viewBox="0 0 24 24" fill="currentColor" {...(rest as any)}>
      <path d={d} />
    </Icon>
  );
};

interface CustomCheckBoxProps extends CheckboxProps {
  name: string;
  onValueChange: (isChecked: boolean) => void;
}

const CustomCheckbox: React.FC<CustomCheckBoxProps> = (props) => {
  const { name, onValueChange, isChecked, ...rest } = props;

  return (
    <Checkbox
      icon={<CustomIcon isChecked={isChecked} />}
      colorScheme="cyan"
      isChecked={isChecked}
      onChange={(e) => onValueChange(e.target.checked)}
      {...rest}
    >
      {name}
    </Checkbox>
  );
};

export default CustomCheckbox;
