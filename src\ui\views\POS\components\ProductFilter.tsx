import React, { useState } from "react";
import { Box, Button, Input, Wrap } from "@chakra-ui/react";

interface Category {
  id: string;
  name: string;
}

interface ProductFilterProps {
  categories: Category[];
  onSearch: (searchTerm: string) => void;
  onFilter: (categoryId: string) => void;
}

const ProductFilter: React.FC<ProductFilterProps> = ({
  categories,
  onSearch,
  onFilter,
}) => {
  const [activeCategory, setActiveCategory] = useState<string>("all");

  const handleFilter = (id: string) => {
    setActiveCategory(id);
    onFilter(id);
  };

  return (
    <Box mb={4}>
      <Input
        placeholder="Search products by name or SKU"
        mb={4}
        onChange={(e) => onSearch(e.target.value)}
      />
      <Wrap>
        <Button
          variant={activeCategory === "all" ? "solid" : "outline"}
          onClick={() => handleFilter("all")}
        >
          All
        </Button>
        {categories.map((category) => (
          <Button
            key={category.id}
            variant={activeCategory === category.id ? "solid" : "outline"}
            onClick={() => handleFilter(category.id)}
          >
            {category.name}
          </Button>
        ))}
      </Wrap>
    </Box>
  );
};

export default ProductFilter;
