import { app } from "electron";
import * as path from "path";
import { ChildProcess, spawn } from "child_process";

export function handleSquirrelEvent(): boolean {
  if (process.argv.length === 1) {
    return false;
  }

  const appFolder = path.resolve(process.execPath, "..");
  const rootAtomFolder = path.resolve(appFolder, "..");
  const updateDotExe = path.resolve(path.join(rootAtomFolder, "Update.exe"));
  const exeName = path.basename(process.execPath);

  // Function to spawn a new process with a command and arguments
  const spawnProcess = (
    command: string,
    args: string[]
  ): ChildProcess | undefined => {
    try {
      return spawn(command, args, { detached: true });
    } catch (error) {
      console.error("Failed to spawn process:", error);
      return undefined;
    }
  };

  // Function to spawn the update process with given arguments
  const spawnUpdate = (args: string[]): ChildProcess | undefined => {
    return spawnProcess(updateDotExe, args);
  };

  // The squirrel event that triggered this process
  const squirrelEvent = process.argv[1];

  switch (squirrelEvent) {
    case "--squirrel-install":
    case "--squirrel-updated":
      // Optionally, you can do tasks like:
      // - Add your .exe to the PATH
      // - Write to the registry for file associations, etc.

      // Install desktop and start menu shortcuts
      spawnUpdate(["--createShortcut", exeName]);

      // Quit the app after a short delay
      setTimeout(() => app.quit(), 1000);
      return true;

    case "--squirrel-uninstall":
      // Undo anything you did in the --squirrel-install or --squirrel-updated handlers

      // Remove desktop and start menu shortcuts
      spawnUpdate(["--removeShortcut", exeName]);

      setTimeout(() => app.quit(), 1000);
      return true;

    case "--squirrel-obsolete":
      // This is called before updating to the new version (opposite of --squirrel-updated)
      app.quit();
      return true;

    default:
      return false;
  }
}
