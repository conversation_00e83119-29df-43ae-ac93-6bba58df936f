import { contextBridge, ipc<PERSON><PERSON><PERSON>, Ipc<PERSON>enderer<PERSON>vent } from "electron";

// Define a type for the API you want to expose
interface ElectronAPI {
  send: (channel: string, data: any) => void;
  on: (
    channel: string,
    callback: (event: IpcRendererEvent, ...args: any[]) => void
  ) => void;
  invoke: (channel: string, data: any) => Promise<any>;
  removeAllListeners: (channel: string) => void;
}

contextBridge.exposeInMainWorld("electron", {
  // Safely send messages to the main process
  send: (channel: string, data: any) => {
    const validChannels = ["app-quit"];
    if (validChannels.includes(channel)) {
      ipcRenderer.send(channel, data);
    }
  },

  // Safely listen for messages from the main process
  on: (
    channel: string,
    callback: (event: IpcRendererEvent, ...args: any[]) => void
  ) => {
    const validChannels = [
      "response-from-main",
      "update-available",
      "update-not-available",
      "download-progress",
      "update-downloaded",
      "update-error",
      "network-status-change",
    ];
    if (validChannels.includes(channel)) {
      ipcRenderer.on(channel, (event: IpcRendererEvent, ...args: any[]) =>
        callback(event, ...args)
      );
    }
  },

  // Safely invoke methods from the main process
  invoke: (channel: string, data: any) => {
    const validChannels = [
      "get-state",
      "clear-image-cache",
      "save-company-id",
      "get-company-id",
      "save-company-sub",
      "get-company-sub",
      "check-network-status",
      "check-for-updates",
      "start-update",
      "save-deployment-type",
      "get-deployment-type",
      "scan-network-for-servers",
      "save-server-url",
      "get-server-url",
    ];
    if (validChannels.includes(channel)) {
      return ipcRenderer.invoke(channel, data);
    }
    return Promise.reject("Channel not allowed");
  },

  removeAllListeners: (channel: string) => {
    const validChannels = [
      "update-available",
      "update-not-available",
      "download-progress",
      "update-downloaded",
      "update-error",
      "network-status-change",
    ];
    if (validChannels.includes(channel)) {
      ipcRenderer.removeAllListeners(channel);
    }
  },
} as ElectronAPI);
