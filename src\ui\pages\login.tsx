/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-empty-object-type */
import loginImage from "../assets/img/login.jpg";
import React, { useEffect, useState } from "react";
import * as Yup from "yup";
import { Formik } from "formik";
import {
  useLoginMutation,
  useSyncHistoryQuery,
  useTriggerSyncMutation,
} from "../generated/graphql";
import { Link, useHistory } from "react-router-dom";
import {
  Button,
  Flex,
  Box,
  Heading,
  Text,
  VStack,
  Image,
  useColorModeValue,
  useBreakpointValue,
  useToast,
  Tooltip,
  Spinner,
  HStack,
  Select,
  FormControl,
  FormLabel,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  ModalCloseButton,
  Radio,
  RadioGroup,
  Stack,
  useDisclosure,
  Progress,
} from "@chakra-ui/react";
import { InputField } from "../views/POS/components/InputField";
import { decrypt, encrypt } from "../utils/Helpers";
import { FaWifi, FaServer, FaExclamationTriangle } from "react-icons/fa";
import { PiWifiSlash } from "react-icons/pi";
import { getApiUrl } from "../utils/CreateUrqlClient";

// Add network status components
const NetworkStatusIndicators: React.FC = () => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [apiStatus, setApiStatus] = useState<
    "connected" | "disconnected" | "checking"
  >("checking");
  const [electronNetworkStatus, setElectronNetworkStatus] = useState<{
    online: boolean;
    details: { dns: boolean; internet: boolean };
  } | null>(null);

  // Add network status listener
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener("online", handleOnline);
    window.addEventListener("offline", handleOffline);

    return () => {
      window.removeEventListener("online", handleOnline);
      window.removeEventListener("offline", handleOffline);
    };
  }, []);

  // Add API health check
  useEffect(() => {
    let isMounted = true;
    let timeoutId: NodeJS.Timeout;

    const checkApiConnection = async () => {
      if (!isOnline) {
        setApiStatus("disconnected");
        return;
      }

      setApiStatus("checking");

      try {
        // Get the API URL based on deployment type
        const apiUrl = await getApiUrl();

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000);

        const response = await fetch(apiUrl, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            query: "{ __typename }",
          }),
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        if (isMounted) {
          setApiStatus(response.ok ? "connected" : "disconnected");
        }
      } catch (error) {
        if (isMounted) {
          console.error("API connection check failed:", error);
          setApiStatus("disconnected");
        }
      }
    };

    // Initial check
    checkApiConnection();

    // Set up periodic checking
    // eslint-disable-next-line prefer-const
    timeoutId = setInterval(checkApiConnection, 10000);

    return () => {
      isMounted = false;
      clearTimeout(timeoutId);
    };
  }, [isOnline]);

  // Add Electron network status check
  useEffect(() => {
    if (window.electron) {
      const checkNetworkStatus = async () => {
        try {
          const status = await window.electron.invoke(
            "check-network-status",
            {}
          );
          setElectronNetworkStatus(status);
          setIsOnline(status.online);
        } catch (err) {
          console.error("Failed to check network status:", err);
          // Fallback to browser's online status
          setIsOnline(navigator.onLine);
        }
      };

      checkNetworkStatus();

      // Set up event listener
      const networkStatusListener = (status: any) => {
        setElectronNetworkStatus(status);
        setIsOnline(status.online);
      };

      window.electron.on("network-status-change", networkStatusListener);

      return () => {
        window.electron.removeAllListeners("network-status-change");
      };
    }
  }, []);

  // Internet status indicator
  const InternetStatusIndicator = () => {
    const networkOnline = electronNetworkStatus
      ? electronNetworkStatus.online
      : isOnline;

    return (
      <Tooltip
        label={
          <Box>
            <Text>
              {networkOnline ? "Internet connected" : "No internet connection"}
            </Text>
          </Box>
        }
        placement="top"
        hasArrow
      >
        <Button
          variant="ghost"
          leftIcon={networkOnline ? <FaWifi /> : <PiWifiSlash />}
          size="sm"
          color={networkOnline ? "green.500" : "red.500"}
        >
          {networkOnline ? "Internet" : "No Internet"}
        </Button>
      </Tooltip>
    );
  };

  // API status indicator
  const ApiStatusIndicator = () => {
    let icon, label, color;

    if (apiStatus === "checking") {
      icon = <Spinner size="xs" />;
      label = "Checking server connection...";
      color = "yellow.500";
    } else if (apiStatus === "connected") {
      icon = <FaServer />;
      label = "Connected to server";
      color = "green.500";
    } else {
      icon = <FaExclamationTriangle />;
      label = "Server unreachable";
      color = "orange.500";
    }

    return (
      <Tooltip label={label} placement="top" hasArrow>
        <Button variant="ghost" leftIcon={icon} size="sm" color={color}>
          {apiStatus === "connected"
            ? "API Online"
            : apiStatus === "checking"
            ? "API Checking..."
            : "API Offline"}
        </Button>
      </Tooltip>
    );
  };

  return (
    <HStack spacing={2}>
      <InternetStatusIndicator />
      <ApiStatusIndicator />
    </HStack>
  );
};

// Add a component to display when all connection attempts fail
const ConnectionErrorDisplay = () => {
  return (
    <Box
      textAlign="center"
      p={4}
      borderRadius="md"
      bg="red.50"
      borderWidth="1px"
      borderColor="red.200"
    >
      <FaExclamationTriangle
        size="2em"
        color="#E53E3E"
        style={{ margin: "0 auto 1rem" }}
      />
      <Heading size="md" color="red.600" mb={2}>
        Connection Error
      </Heading>
      <Text mb={4}>
        Unable to connect to the server. Please check your network connection
        and try again.
      </Text>
      <Button
        colorScheme="red"
        size="sm"
        onClick={() => window.location.reload()}
        leftIcon={<FaWifi />}
      >
        Retry Connection
      </Button>
    </Box>
  );
};

// Add SyncProgress component
const SyncProgress: React.FC = () => {
  return (
    <Box
      textAlign="center"
      p={4}
      borderRadius="md"
      bg="blue.50"
      borderWidth="1px"
      borderColor="blue.200"
      mb={4}
    >
      <Spinner size="sm" color="blue.500" mr={2} />
      <Text as="span" color="blue.600" fontWeight="medium">
        Syncing data...
      </Text>
    </Box>
  );
};

interface IAppProps {}

const Login: React.FunctionComponent<IAppProps> = () => {
  const [, login] = useLoginMutation();
  const [{ fetching: waitingSync }, triggerSync] = useTriggerSyncMutation();
  const [general, setGeneral] = React.useState("");
  const [deploymentChange, setDeploymentChange] = React.useState(false);
  const [deploymentType, setDeploymentType] = useState<
    "server" | "lan" | "online"
  >("online");
  const history = useHistory();
  const toast = useToast({ position: "top", duration: 3000 });
  const [isScanning, setIsScanning] = useState(false);
  const [companyId, setCompanyId] = useState<number>(0);
  const [foundServers, setFoundServers] = useState<string[]>([]);
  const [selectedServer, setSelectedServer] = useState<string>("");
  const [companyResult, setCompanyResult] = useState<boolean>();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [isConnected, setIsConnected] = useState(true);

  const cardBg = useColorModeValue("white", "gray.800");
  const bgGradient = useColorModeValue(
    "linear(to-r, teal.100, blue.200)",
    "linear(to-r, teal.900, blue.800)"
  );
  const formHeadingColor = useColorModeValue("teal.600", "teal.300");
  const headingSize = useBreakpointValue({ base: "lg", md: "xl" });
  const boxBgGradient = useColorModeValue(
    "linear(to-b, white, gray.50)",
    "linear(to-b, gray.700, gray.800)"
  );
  const formLabelColor = useColorModeValue("gray.600", "gray.400");

  const [{ data }, syncHistory] = useSyncHistoryQuery({
    requestPolicy: "network-only",
    variables: { companyId: companyId, entityName: "Company" },
  });

  // Load deployment type preference on component mount
  useEffect(() => {
    const loadDeploymentType = async () => {
      try {
        // First check if we have a company ID
        const companyResult = await window.electron.invoke(
          "get-company-id",
          {}
        );

        // If no company ID, force online mode by default
        if (!companyResult.companyId) {
          setDeploymentType("online");
          setCompanyResult(false);
          return;
        }
        if (companyResult && companyResult.companyId) {
          setCompanyResult(companyResult.companyId > 0);
          setCompanyId(Number(companyResult.companyId));
        }
        // Otherwise load the saved deployment type
        const result = await window.electron.invoke("get-deployment-type", {});
        if (result.error) {
          console.error("Error loading deployment type:", result.error);
          setDeploymentType("online");
        } else {
          setDeploymentType(
            result.isServer !== null &&
              result.isServer !== undefined &&
              result.isServer === true
              ? "server"
              : result.isServer !== null &&
                result.isServer !== undefined &&
                result.isServer === false
              ? "lan"
              : "online"
          );
          if (
            companyId > 0 &&
            (result.isServer === null || result.isServer === undefined)
          ) {
            // check if there are any syncHistory for Company Entity
            await syncHistory({
              requestPolicy: "network-only",
              variables: { companyId: companyId, entityName: "Company" },
            });
            if (data && data?.syncHistory) {
              if (data?.syncHistory.length >= 2) {
                // at least two syncHistory (one up and one down) for Company Entity were successful
                const companyUpSync = data?.syncHistory.find(
                  (sync) =>
                    sync.entityName === "Company" && sync.direction === "up"
                );
                const companyDownSync = data?.syncHistory.find(
                  (sync) =>
                    sync.entityName === "Company" && sync.direction === "down"
                );
                if (companyUpSync && companyDownSync) {
                  setDeploymentChange(true);
                }
              }
            }
          }
        }
      } catch (error) {
        console.error("Failed to get deployment type:", error);
        setDeploymentType("online");
      }
    };

    loadDeploymentType();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Function to scan the network for servers
  const scanNetwork = async () => {
    setIsScanning(true);
    setFoundServers([]);

    try {
      const result = await window.electron.invoke(
        "scan-network-for-servers",
        {}
      );

      if (result.success && result.servers && result.servers.length > 0) {
        setFoundServers(result.servers);
        setSelectedServer(result.servers[0]);
        onOpen();
      } else {
        toast({
          title: "No servers found",
          description:
            result.error || "Could not find any servers on your local network.",
          status: "warning",
          isClosable: true,
        });
      }
    } catch (error: any) {
      console.error("Error scanning network:", error);
      toast({
        title: "Scan failed",
        description:
          error?.message || "Failed to scan the network for servers.",
        status: "error",
        isClosable: true,
      });
    } finally {
      setIsScanning(false);
    }
  };

  // Function to save the selected server
  const saveSelectedServer = async () => {
    if (!selectedServer) return;

    try {
      const result = await window.electron.invoke(
        "save-server-url",
        selectedServer
      );
      const deploymentResult = await window.electron.invoke(
        "save-deployment-type",
        { isServer: false }
      );

      if (result.success && deploymentResult.success) {
        toast({
          title: "Server saved",
          description: "The selected server has been saved.",
          status: "success",
          isClosable: true,
        });
        onClose();

        // Refresh the page to use the new server URL
        window.location.reload();
      } else {
        toast({
          title: "Failed to save server url to device, contact support!",
          description: result.error,
          status: "error",
          isClosable: true,
        });
      }
    } catch (error) {
      console.error("Error saving server URL:", error);
      toast({
        title: "Failed to save server url to device, contact support!",
        status: "error",
        isClosable: true,
      });
    }
  };

  // Handle deployment type change
  const handleDeploymentTypeChange = async (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    const newType = e.target.value as "server" | "lan" | "online";
    try {
      const triggerResult = await triggerSync({
        companyId: companyId,
      });
      if (triggerResult.data?.triggerSync.status === true) {
        toast({
          title: "Sync completed successfully, switching server mode",
          status: "success",
          isClosable: true,
        });
      } else {
        toast({
          title: "Sync failed",
          description: "Sync not successful due to server error!",
          status: "error",
          isClosable: true,
        });
        toast({
          title: "Switching server failed",
          description: "Switching server requires successful sync, try again!",
          status: "error",
          isClosable: true,
        });
        return;
      }

      const result = await window.electron.invoke("save-deployment-type", {
        isServer:
          newType === "server" ? true : newType === "lan" ? false : null,
      });

      if (result.success) {
        // If switching to LAN mode, prompt to scan for servers
        if (newType === "lan") {
          try {
            const serverUrlResult = await window.electron.invoke(
              "get-server-url",
              {}
            );
            if (!serverUrlResult.serverUrl) {
              // Only show this if no server URL is saved yet
              toast({
                title: "LAN mode activated",
                description: "Click 'Scan Network' to find available servers.",
                status: "info",
                duration: 5000,
                isClosable: true,
              });
            }
          } catch (error) {
            console.error("Failed to get server URL:", error);
            return toast({
              title: "Failed to check saved servers",
              status: "warning",
              isClosable: true,
            });
          }
        }
        setDeploymentType(newType);
        toast({
          title: `Switched to ${newType} mode`,
          status: "success",
          isClosable: true,
        });
      } else {
        toast({
          title: "Failed to save server preference",
          description: result.error || "Unknown error occurred",
          status: "error",
          isClosable: true,
        });
      }
    } catch (error: any) {
      console.error("Failed to save deployment type:", error);
      toast({
        title: "Failed to save preference",
        description: error?.message || "An unexpected error occurred",
        status: "error",
        isClosable: true,
      });
    }
  };

  // Add this effect to monitor connection status
  useEffect(() => {
    if (window.electron) {
      const handleNetworkStatus = (status: any) => {
        setIsConnected(status.online && status.api);
      };
      window.electron.on("network-status-change", handleNetworkStatus);
      return () => {
        window.electron.removeAllListeners("network-status-change");
      };
    }
  }, []);

  return (
    <Flex
      align="center"
      justify="center"
      minH="100vh"
      bgGradient={bgGradient}
      p={6}
    >
      {!isConnected ? (
        <ConnectionErrorDisplay />
      ) : (
        <>
          <Modal isOpen={isOpen} onClose={onClose}>
            <ModalOverlay />
            <ModalContent>
              <ModalHeader>Select Server</ModalHeader>
              <ModalCloseButton />
              <ModalBody>
                <Text mb={4}>
                  The following servers were found on your network. Please
                  select one:
                </Text>
                <RadioGroup value={selectedServer} onChange={setSelectedServer}>
                  <Stack>
                    {foundServers.map((server) => (
                      <Radio key={server} value={server}>
                        {server}
                      </Radio>
                    ))}
                  </Stack>
                </RadioGroup>
              </ModalBody>
              <ModalFooter>
                <Button variant="ghost" mr={3} onClick={onClose}>
                  Cancel
                </Button>
                <Button colorScheme="blue" onClick={saveSelectedServer}>
                  Save
                </Button>
              </ModalFooter>
            </ModalContent>
          </Modal>
          <Box
            bg={cardBg}
            boxShadow="2xl"
            borderRadius="xl"
            overflow="hidden"
            maxW={{ base: "90%", md: "60%", lg: "50%" }}
            display={{ base: "block", md: "flex" }}
          >
            <Box flex="1">
              <Image
                src={loginImage}
                alt="Login Image"
                objectFit="cover"
                height="100%"
                width="100%"
              />
            </Box>
            <Box p={8} flex="1" bgGradient={boxBgGradient}>
              <Flex justify="space-between" align="center" mb={6}>
                <Heading
                  as="h2"
                  size={headingSize}
                  textAlign="center"
                  color={formHeadingColor}
                  fontWeight="bold"
                  letterSpacing="wide"
                >
                  Login
                </Heading>
                <NetworkStatusIndicators />
              </Flex>

              {waitingSync && <SyncProgress />}

              {/* Deployment Type Selector */}
              <FormControl mb={5}>
                <FormLabel
                  htmlFor="deploymentType"
                  fontSize="xs"
                  fontWeight="medium"
                  textTransform="uppercase"
                  letterSpacing="wider"
                  color={formLabelColor}
                >
                  Connection Mode
                </FormLabel>
                {waitingSync ? (
                  // tell user we are waiting for sync to complete
                  <>
                    <Text color="red.500" fontWeight="bold">
                      Waiting for sync to complete...
                    </Text>
                    <Progress maxW="240px" isIndeterminate />
                  </>
                ) : (
                  <HStack spacing={3}>
                    <Select
                      id="deploymentType"
                      value={deploymentType}
                      onChange={handleDeploymentTypeChange}
                      disabled={!deploymentChange}
                      size="sm"
                      variant="filled"
                      flex="1"
                      borderRadius="md"
                      boxShadow="sm"
                      _focus={{ boxShadow: "outline" }}
                      bg={"gray.50"}
                      iconColor={"teal.500"}
                      isDisabled={!companyResult}
                    >
                      <option value="online">Online</option>
                      <option value="server">On Server</option>
                      <option value="lan">On LAN</option>
                    </Select>
                    {deploymentType === "lan" && (
                      <Button
                        size="sm"
                        colorScheme="teal"
                        onClick={scanNetwork}
                        isLoading={isScanning}
                        loadingText="Scanning"
                        borderRadius="md"
                        boxShadow="sm"
                        _hover={{ transform: "translateY(-1px)" }}
                        transition="all 0.2s"
                        leftIcon={<FaWifi />}
                      >
                        Scan
                      </Button>
                    )}
                  </HStack>
                )}
              </FormControl>

              {general ? (
                <Text mb={4} color="red.500" fontWeight="bold">
                  {general}
                </Text>
              ) : null}
              <Formik
                initialValues={{ email: "", password: "" }}
                onSubmit={async (values, { setSubmitting }) => {
                  try {
                    if (navigator.onLine === false) {
                      toast({
                        title: "Server connection error",
                        description:
                          "Cannot connect to the server. Please check your network connection.",
                        status: "error",
                        isClosable: true,
                      });
                      setGeneral(
                        "Cannot connect to server. Please check your network connection."
                      );
                      return;
                    }

                    const user = await login({ params: values });

                    if (user.data?.login.error) {
                      setGeneral(user.data.login.error.message);
                    } else if (user.data?.login.user) {
                      const companyId = user.data.login.user.companyId;
                      if (companyId) {
                        try {
                          const result = await window.electron.invoke(
                            "save-company-id",
                            encrypt(`${companyId}`)
                          );
                          if (!result.success) {
                            console.warn(
                              "Failed to save company ID:",
                              result.error
                            );

                            const oldRes = await window.electron.invoke(
                              "get-company-id",
                              {}
                            );
                            if (Number(decrypt(oldRes.companyId)) <= 0)
                              toast({
                                title:
                                  "Quick login setup failed, using normal login next time",
                                status: "warning",
                                isClosable: true,
                              });
                          } else {
                            toast({
                              title: "Quick Login Activated for POS users",
                              status: "success",
                              isClosable: true,
                            });
                          }
                          // activate sync
                          const triggerResult = await triggerSync({
                            companyId: companyId,
                          });
                          if (triggerResult.data?.triggerSync.status === true) {
                            toast({
                              title: "Sync completed successfully",
                              status: "success",
                              isClosable: true,
                            });
                          } else {
                            toast({
                              title: "Sync failed",
                              description:
                                "Sync not successful due to server error!",
                              status: "error",
                              isClosable: true,
                            });
                          }
                        } catch (error) {
                          console.error("IPC error saving company ID:", error);
                          toast({
                            title:
                              "Quick login setup failed, using normal login next time",
                            status: "warning",
                            isClosable: true,
                          });
                        }
                      }
                      if (
                        user.data?.login.user.company.id === 0 &&
                        user.data?.login.user.role.name === "admin"
                      ) {
                        console.log("this is loading the troubleshoot page");
                        history.replace("/desktop/troubleshoot");
                      } else {
                        history.replace("/desktop/POS");
                      }
                    }
                  } catch (error: any) {
                    console.error("Login request failed:", error);
                    setGeneral(
                      error?.message ||
                        "An unexpected error occurred. Please try again."
                    );
                    toast({
                      title: "Login failed",
                      description:
                        error?.message ||
                        "An unexpected error occurred. Please try again.",
                      status: "error",
                      isClosable: true,
                    });
                  } finally {
                    setSubmitting(false);
                  }
                }}
                validationSchema={Yup.object({
                  email: Yup.string()
                    .email("Enter a valid email address!")
                    .required("Required"),
                  password: Yup.string()
                    .min(3, "Must be 3 or more characters!")
                    .max(20, "Must be 20 characters or less!")
                    .required("Required"),
                })}
                validateOnChange={false}
              >
                {(props) => (
                  <form
                    onSubmit={(e) => {
                      e.preventDefault();
                      props.submitForm();
                    }}
                  >
                    <VStack spacing={4}>
                      <InputField
                        error={props.errors.email}
                        name="email"
                        touched={props.touched.email}
                        label="Email"
                        placeholder="E-mail address"
                        type="email"
                      />
                      <InputField
                        error={props.errors.password}
                        name="password"
                        label="Password"
                        touched={props.touched.password}
                        placeholder="Password"
                        type="password"
                      />
                    </VStack>
                    <Button
                      isLoading={props.isSubmitting}
                      colorScheme="teal"
                      size="lg"
                      type="submit"
                      width="full"
                      mt={6}
                    >
                      Login
                    </Button>
                  </form>
                )}
              </Formik>
              <Text mt={4} textAlign="center">
                Forgot password?{" "}
                <Link to="/forgot-password" style={{ color: "#319795" }}>
                  Recover
                </Link>
              </Text>
              <Button
                as={Link}
                to="sign-in"
                variant={"ghost"}
                width={"full"}
                color={"blue.400"}
              >
                Quick Sign In
              </Button>
            </Box>
          </Box>
        </>
      )}
    </Flex>
  );
};

export default Login;
