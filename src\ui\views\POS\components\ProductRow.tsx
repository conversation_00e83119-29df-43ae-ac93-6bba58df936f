/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  Badge,
  Button,
  Flex,
  Icon,
  Td,
  Text,
  Tr,
  useColorModeValue,
} from "@chakra-ui/react";
import { FaPencilAlt, FaEye } from "react-icons/fa";
import { useHistory } from "react-router-dom";
import { Item } from "../../../generated/graphql";

function ProductRow(props: {
  item: Item;
  role: string;
  isLast: boolean;
  history: any;
  callOnClick: any;
}) {
  const { item, isLast } = props;

  const history = useHistory();

  const textColor = useColorModeValue("gray.500", "white");
  const titleColor = useColorModeValue("gray.700", "white");
  const borderColor = useColorModeValue("gray.200", "gray.600");

  return (
    <Tr ml={5}>
      <Td
        minWidth={{ sm: "250px" }}
        pl="5px"
        borderColor={borderColor}
        borderBottom={isLast ? ("none" as any) : null}
      >
        <Text
          fontSize="md"
          color={titleColor}
          fontWeight="bold"
          minWidth="100%"
        >
          {item.name}
        </Text>
      </Td>
      <Td
        minWidth={{ sm: "250px" }}
        pl="0px"
        borderColor={borderColor}
        borderBottom={isLast ? ("none" as any) : null}
      >
        <Text
          fontSize="md"
          color={titleColor}
          fontWeight="bold"
          minWidth="100%"
        >
          {item.description}
        </Text>
      </Td>

      <Td
        borderColor={borderColor}
        borderBottom={isLast ? ("none" as any) : null}
      >
        <Text fontSize="md" color={textColor} fontWeight="bold">
          {item.type}
        </Text>
      </Td>
      <Td
        borderColor={borderColor}
        borderBottom={isLast ? ("none" as any) : null}
      >
        {item.stock > item.reorder ? (
          <Badge
            bg={"green.400"}
            color={item.stock < item.reorder ? "white" : "white"}
            fontSize="16px"
            p="3px 10px"
            borderRadius="8px"
          >
            {item.stock + " " + item.unit}{" "}
          </Badge>
        ) : item.stock <= item.reorder && item.stock > 0 ? (
          <Badge
            bg={"red.400"}
            color={item.stock < item.reorder ? "white" : "white"}
            fontSize="16px"
            p="3px 10px"
            borderRadius="8px"
          >
            {item.stock + " " + item.unit}{" "}
          </Badge>
        ) : item.stock === 0 ? (
          <Badge
            bg={"red.400"}
            color={item.stock < item.reorder ? "white" : "white"}
            fontSize="16px"
            p="3px 10px"
            borderRadius="8px"
          >
            {item.stock + " " + item.unit}{" "}
          </Badge>
        ) : null}
      </Td>
      <Td
        borderColor={borderColor}
        borderBottom={isLast ? ("none" as any) : null}
      >
        <Flex
          direction={{ sm: "column", md: "row" }}
          align="flex-start"
          p={{ md: "0px" }}
        >
          <Button
            onClick={() => {
              return props.history.push({
                pathname: `manage-product`,
                state: {
                  item: item,
                },
              });
            }}
            p="0px"
            bg="transparent"
            variant="no-effects"
          >
            <Flex color={textColor} cursor="pointer" align="center" p="12px">
              <Icon as={FaPencilAlt} me="4px" />
              <Text fontSize="sm" fontWeight="semibold">
                EDIT
              </Text>
            </Flex>
          </Button>
          {/* <Button
            p="0px"
            bg="transparent"
            variant="no-effects"
            mb={{ sm: "10px", md: "0px" }}
            me={{ md: "12px" }}
            onClick={props.callOnClick}
          >
            <Flex color="red.200" cursor="pointer" align="center" p="12px">
              <Icon as={FaTrashAlt} me="4px" />
              <Text fontSize="sm" fontWeight="semibold">
                DELETE
              </Text>
            </Flex>
          </Button> */}
          <Button
            p="0px"
            variant="solid"
            mb={{ sm: "10px", md: "0px" }}
            me={{ md: "12px" }}
            onClick={() =>
              history.push({
                pathname: "product",
                state: {
                  item,
                },
              })
            }
          >
            <Flex color="navy.600" cursor="pointer" align="center" p="12px">
              <Icon as={FaEye} me="4px" />
              <Text fontSize="sm" fontWeight="semibold">
                VIEW
              </Text>
            </Flex>
          </Button>
        </Flex>
      </Td>
    </Tr>
  );
}

export default ProductRow;
