// This script generates self-signed certificates for development
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

const certDir = path.join(__dirname, '../certificates');
const keyPath = path.join(certDir, 'localhost-key.pem');
const certPath = path.join(certDir, 'localhost.pem');

// Create certificates directory if it doesn't exist
if (!fs.existsSync(certDir)) {
  fs.mkdirSync(certDir, { recursive: true });
  console.log('Created certificates directory');
}

// Generate certificates if they don't exist
if (!fs.existsSync(keyPath) || !fs.existsSync(certPath)) {
  console.log('Generating self-signed certificates...');
  
  try {
    // Using OpenSSL to generate self-signed certificates
    execSync(`openssl req -x509 -newkey rsa:2048 -keyout "${keyPath}" -out "${certPath}" -days 365 -nodes -subj "/CN=localhost"`, 
      { stdio: 'inherit' });
    
    console.log('Certificates generated successfully');
  } catch (error) {
    console.error('Failed to generate certificates:', error);
    process.exit(1);
  }
} else {
  console.log('Certificates already exist');
}