import React from "react";
import { Flex, Box, IconButton } from "@chakra-ui/react";
import { BsPower } from "react-icons/bs";
import { confirmAlert } from "react-confirm-alert"; // Import the confirmAlert package
import "react-confirm-alert/src/react-confirm-alert.css"; // Import CSS for react-confirm-alert
import "../styles/pos.css";
import { print } from "graphql";
import axios from "axios";
import gql from "graphql-tag";

const Navbar: React.FC = () => {
  // Function to handle app quit with confirmation
  const handleQuit = async () => {
    confirmAlert({
      title: "Confirm to Quit",
      message: "Are you sure you want to quit the application?",
      buttons: [
        {
          label: "Yes",
          onClick: async () => {
            // Send the 'app-quit' event to the main process if user confirms
            const query = gql`
            mutation Logout() {
              logout() {
              }
            }
          `;

            const response = await axios.post("http://localhost:4000/graphql", {
              query: print(query),
              withCredentials: true,
            });
            console.log("this is the reposnse:", response);

            window.electron.send("app-quit", {});
          },
        },
        {
          label: "No",
          onClick: () => {
            // Do nothing if user cancels
            console.log("Quit operation cancelled");
          },
        },
      ],
    });
  };

  return (
    <Flex
      as="nav"
      bg="blue.500"
      color="white"
      p={4}
      align="center"
      justify="space-between" // Distribute space between elements (left and right)
      position="relative" // Necessary for absolute positioning of quit button
    >
      <Box fontWeight="bold" fontSize="lg" mr={4}>
        POS
      </Box>

      {/* Quit button floated to the right */}
      <IconButton
        icon={<BsPower />}
        aria-label="Quit App"
        bg="red.500"
        color="white"
        _hover={{ bg: "red.400" }}
        onClick={handleQuit} // Call handleQuit on click
        position="absolute"
        top={2}
        right={4} // This positions the quit button on the top-right corner
      />
    </Flex>
  );
};

export default Navbar;
