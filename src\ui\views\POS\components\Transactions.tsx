import React from "react";
import {
  Box,
  Heading,
  Table,
  Tbody,
  Td,
  Th,
  Thead,
  Tr,
} from "@chakra-ui/react";

const Transactions: React.FC = () => {
  const transactions = [
    { id: "1", date: "2024-11-15", total: 500, cashier: "<PERSON>" },
    { id: "2", date: "2024-11-14", total: 300, cashier: "Doe" },
  ];

  return (
    <Box>
      <Heading mb={4}>Transactions</Heading>
      <Table variant="striped">
        <Thead>
          <Tr>
            <Th>ID</Th>
            <Th>Date</Th>
            <Th>Total</Th>
            <Th>Cashier</Th>
          </Tr>
        </Thead>
        <Tbody>
          {transactions.map((tx) => (
            <Tr key={tx.id}>
              <Td>{tx.id}</Td>
              <Td>{tx.date}</Td>
              <Td>${tx.total}</Td>
              <Td>{tx.cashier}</Td>
            </Tr>
          ))}
        </Tbody>
      </Table>
    </Box>
  );
};

export default Transactions;
