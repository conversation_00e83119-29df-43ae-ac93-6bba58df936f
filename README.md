# Talisia POS - Modern Point of Sale System

![Talisia POS](https://via.placeholder.com/800x400?text=<PERSON><PERSON><PERSON>+POS)

Talisia POS is a comprehensive point of sale desktop application built with modern web technologies. It provides businesses with a powerful, intuitive, and feature-rich solution for managing sales, inventory, employees, and more.

## 🚀 Features

### Core Functionality
- **Sales Processing**: Fast and intuitive checkout experience
- **Inventory Management**: Track stock levels and product details
- **Employee Management**: Manage staff accounts, roles, and permissions
- **Customer Management**: Store customer information and purchase history
- **Expense Tracking**: Monitor and categorize business expenses
- **Reporting**: Generate detailed sales and inventory reports

### Technical Features
- **Modern UI**: Clean, responsive interface with dark/light mode support
- **Offline Capability**: Continue operations during internet outages
- **Multi-device Sync**: Seamlessly sync data across devices
- **Role-based Access Control**: Secure access based on user roles
- **Real-time Updates**: Instant data synchronization

## 🖥️ Technology Stack

- **Frontend**: React, TypeScript, Chakra UI
- **State Management**: Urql for GraphQL client
- **Backend**: GraphQL API
- **Database**: PostgreSQL with Supabase
- **Build Tool**: Vite
- **Desktop Packaging**: Electron

## 📋 Prerequisites

- Node.js (v16+)
- npm or yarn
- Git

## 🛠️ Installation

```bash
# Clone the repository
git clone https://github.com/yourusername/talisia-desktop.git

# Navigate to the project directory
cd talisia-desktop

# Install dependencies
npm install

# Start the development server
npm run dev
```

## 🔧 Configuration

Create a `.env` file in the root directory with the following variables:

```
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
VITE_API_URL=your_graphql_api_url
```

## 📱 Usage

### User Authentication

The system supports multiple authentication methods:
- Username/password login
- Employee card scanning
- Quick user selection with PIN

### Sales Process

1. Select products from the catalog or scan barcodes
2. Apply discounts or promotions if applicable
3. Process payment (cash, card, or split payment)
4. Generate receipt

### Inventory Management

- Add, edit, or remove products
- Track stock levels
- Set up low stock alerts
- Manage product categories

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📞 Support

For support, email <EMAIL> or open an issue in the GitHub repository.
