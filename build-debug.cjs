const { build, Platform } = require("electron-builder");
const path = require("path");
const fs = require("fs");
const { execSync } = require("child_process");

// Enable verbose logging
process.env.DEBUG = "electron-builder";

console.log("Starting build process...");

// Parse command line arguments
const args = process.argv.slice(2);
const shouldPublish = args.includes("--publish") || args.includes("-p");
const publishMode = shouldPublish ? "always" : "never";

console.log(`Build mode: ${shouldPublish ? "publish" : "build only"}`);

// Run the build commands
try {
  console.log("Transpiling Electron code...");
  execSync("yarn transpile:electron", { stdio: "inherit" });

  console.log("Building React app...");
  execSync("yarn build", { stdio: "inherit" });
} catch (error) {
  console.error("Build step failed:", error);
  process.exit(1);
}

console.log("Starting electron-builder...");

// Check if the required directories exist
console.log("Checking directories...");
const distElectronExists = fs.existsSync("./dist-electron");
const distReactExists = fs.existsSync("./dist-react");
console.log(`dist-electron exists: ${distElectronExists}`);
console.log(`dist-react exists: ${distReactExists}`);

// Check if the main entry point exists
const mainJsPath = "./dist-electron/main.js";
const mainJsExists = fs.existsSync(mainJsPath);
console.log(`Main entry point (${mainJsPath}) exists: ${mainJsExists}`);

// Use a simplified configuration --- we want to move to electron-builder soon
build({
  targets: Platform.WINDOWS.createTarget(["nsis"]),
  config: {
    appId: "com.jecstz.talisia-desktop",
    productName: "Talisia POS",
    files: ["dist-electron/**/*", "dist-react/**/*"],
    directories: {
      output: path.join(process.cwd(), "release"),
    },
    win: {
      target: "nsis",
    },
    publish: shouldPublish
      ? {
          provider: "github",
          owner: "samxtu",
          repo: "Talisia-POS",
        }
      : undefined,
  },
  publish: publishMode,
})
  .then((result) => {
    console.log("Build completed successfully!");
    console.log(result);
  })
  .catch((error) => {
    console.error("Build failed with error:");
    console.error(error);
  });
