import React, { createContext, ReactNode } from "react";
import { MeQuery } from "../generated/graphql";

interface IWrapperProps {
  me: MeQuery["me"];
  children: ReactNode;
}

export const MeContext = createContext<MeQuery["me"] | undefined>(undefined);

export const Wrapper: React.FC<IWrapperProps> = ({ me, children }) => {
  return <MeContext.Provider value={me}>{children}</MeContext.Provider>;
};
