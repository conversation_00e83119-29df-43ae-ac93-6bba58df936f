/* Style for the react-confirm-alert overlay */
.react-confirm-alert-overlay {
  background: rgba(0, 0, 0, 0.5);
}

/* Style for the confirm alert container */
.react-confirm-alert-body {
  background-color: #fefefe;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  max-width: 400px;
  padding: 30px;
  text-align: center;
}

/* Title of the confirmation dialog */
.react-confirm-alert-title {
  font-size: 1.5rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 20px;
}

/* Message styling */
.react-confirm-alert-message {
  font-size: 1rem;
  color: #555;
  margin-bottom: 30px;
}

/* Style for the buttons container */
.react-confirm-alert-button-group {
  display: flex;
  justify-content: space-between;
  gap: 10px;
}

/* Custom styling for Yes button */
.react-confirm-alert-button.react-confirm-alert-yes {
  background-color: #e63946;
  color: white;
  padding: 10px 20px;
  font-size: 1.1rem;
  border-radius: 5px;
  border: none;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.react-confirm-alert-button.react-confirm-alert-yes:hover {
  background-color: #d62828;
}

/* Custom styling for No button */
.react-confirm-alert-button.react-confirm-alert-no {
  background-color: #6c757d;
  color: white;
  padding: 10px 20px;
  font-size: 1.1rem;
  border-radius: 5px;
  border: none;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.react-confirm-alert-button.react-confirm-alert-no:hover {
  background-color: #5a6268;
}

/* Center the confirm alert modal */
.react-confirm-alert {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}
