import { forwardRef } from "react";
import { Box, Text, Table, Tbody, Tr, Td } from "@chakra-ui/react";
import { Inventory, Item, Transfer } from "../../../generated/graphql";
import { formatToMoney } from "../../../utils/Helpers";

interface ReceiptProps {
  sale: Inventory;
  total: number;
}

const Receipt = forwardRef<HTMLDivElement, ReceiptProps>(
  ({ sale, total }, ref) => {
    return (
      <Box ref={ref} p="4" fontSize="sm">
        <Text fontSize="lg" fontWeight="bold" mb="4">
          EFD Receipt
        </Text>
        <Text mb="2">Sale ID: {sale.id}</Text>
        <Text mb="2">Date: {new Date().toLocaleString()}</Text>
        <Table variant="simple">
          <Tbody>
            {sale.items.map((item: Item, index: number) => {
              const itemTransfer = sale.transfers.find(
                (transfer: Transfer) => transfer.itemId === item.id
              );
              return (
                <Tr key={index}>
                  <Td>{item.name}</Td>
                  <Td>
                    {itemTransfer!.quantity > 0
                      ? itemTransfer!.quantity + " " + item?.unit
                      : " "}
                  </Td>
                  <Td>
                    {formatToMoney(
                      itemTransfer!.quantity > 0
                        ? item.sellingPrice * itemTransfer!.quantity
                        : 0
                    )}
                  </Td>
                </Tr>
              );
            })}
          </Tbody>
        </Table>
        <Text mt="4" fontWeight="bold">
          Total Sale Amount: {formatToMoney(total)}
        </Text>
      </Box>
    );
  }
);

export default Receipt;
